<template>
    <div class="table-box">
        <div class="headers-container">
            <div class="u-flex">
                <div class="area-select">所在区域</div>
                <RegionTree
                    :data="regionTreeData"
                    @change="regionTreeChange"
                    ref="regionCascader"
                ></RegionTree>
            </div>
            <div class="u-flex" style="gap: 20px">
                <el-dropdown @command="handleOptCommand">
                    <el-button type="primary">
                        {{ optBtnText
                        }}<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                            v-for="(item, index) in optList"
                            :key="index"
                            :command="item"
                            >{{ item.label }}</el-dropdown-item
                        >
                    </el-dropdown-menu>
                </el-dropdown>

                <el-button
                    type="primary"
                    v-if="getViewActionDisplayInfo('auto_allocation')"
                    @click="goAllocation()"
                    :loading="isInAllocation"
                    >{{ isInAllocation ? `任务分配中` : "分配任务" }}</el-button
                >

                <el-button
                    type="primary"
                    v-if="getViewActionDisplayInfo('re_auto_allocation')"
                    @click="goReAllocation"
                    >重新分配任务</el-button
                >
                <el-button type="primary" @click="exportExcel" v-if="showExport"
                    >导出</el-button
                >

                <!-- <el-button
                v-if="descArr && descArr.length"
                @click="displayDetailCollectDesc = true"
                type="info"
                >统计内容字段介绍</el-button
            > -->
            </div>
        </div>
        <div class="table-box" id="task-table-list-detail">
            <table-container
                v-if="getViewDisplayInfo('collect_task') && tableConfig"
                v-show="total"
                showTableFilter
                filedWidth="200"
                ref="table"
                v-model="tableConfig"
                class="container-index container shadow collect-task-table"
                :class="`cur-city-${level}`"
                @setTotal="setTotal"
                @loaded="loaded"
            >
                <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                    <common-table
                        :data="handleData(data)"
                        v-if="showTable"
                        :columns="columns"
                    >
                        <div
                            slot="region_name"
                            slot-scope="{ row, index }"
                            @click="handleRegionNameClick(row, index)"
                        >
                            <div :class="index === 0 ? '' : 'active'">
                                {{ row.region_name }}
                            </div>
                        </div>
                        <div slot="target_add_num" slot-scope="scope">
                            <div v-if="scope.row.isSum">
                                {{ scope.row.target_add_num }}
                            </div>
                            <div
                                v-else-if="editIds.includes(scope.row.id)"
                                class="input-box"
                            >
                                <el-input-number
                                    v-model="commonValue"
                                    controls-position="right"
                                    :min="0"
                                    :max="999999999"
                                    @keyup.enter.native="
                                        updateNum(scope.row.id)
                                    "
                                ></el-input-number>

                                <div class="opt-icon-box">
                                    <div>
                                        <i
                                            @click="updateNum(scope.row.id)"
                                            class="el-icon-circle-check"
                                        ></i>
                                    </div>
                                    <div>
                                        <i
                                            class="el-icon-circle-close"
                                            @click="onReset(scope.row.id)"
                                        ></i>
                                    </div>
                                </div>
                            </div>

                            <div
                                class="opt-box"
                                v-else
                                @click="
                                    openEditNum(
                                        scope.row.id,
                                        scope.row.target_add_num
                                    )
                                "
                            >
                                {{ +(scope.row.target_add_num || "0") || "-" }}
                                <i class="el-icon-edit"></i>
                            </div>
                        </div>
                    </common-table>
                </div>
            </table-container>

            <div class="empty-box" v-show="!total">
                <img src="/img/icon/empty.png" />
                <div class="empty-tips">暂无相关数据</div>

                <el-button
                    type="primary"
                    size="small"
                    v-if="
                        getViewDisplayInfo('collect_task') &&
                        getViewActionDisplayInfo('auto_set_target_behavior')
                    "
                    @click="setTarget()"
                    >设置目标</el-button
                >
            </div>
        </div>
        <DetailCollectDesc
            :descArr="descArr"
            v-model="displayDetailCollectDesc"
        ></DetailCollectDesc>
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { Component, Prop, Ref } from "vue-property-decorator"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { tableConfig, columns, columns2 } from "../detail"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import PieCharts from "../components/pie-charts.vue"
    import DialogProcess from "../components/dialog-process.vue"
    import { DetailRow } from "../model"
    import {
        CURRENT_REGION_NODE_LEVEL,
        CURRENT_TREE_REGION_NODE,
        DetailController,
        levelCodes,
    } from "../base"
    import { sdk } from "@/service"
    import { cloneDeep } from "lodash"
    import {
        DCollectSysConfig,
        getSys4DCollectByKey,
    } from "../../../collect-task-config"
    import { applyTableStyles } from "./table-color-config"
    import DetailCollectDesc from "../components/dialog-collect-desc.vue"
    import XLSX from "xlsx-js-style"
    import RegionTree from "@/views/pages/collect-task-manage/labor-info-base-manage/components/region-tree.vue"
    import { getRegionName } from "@/views/pages/company-task-manage/components/region-tool"

    @Component({
        components: {
            TableContainer,
            CommonTable,
            PieCharts,
            DialogProcess,
            DetailCollectDesc,
            RegionTree,
        },
    })
    export default class DetailList extends BaseTableController<any> {
        @Ref()
        private regionCascader: any

        @Prop({ default: () => null })
        private detailRow!: DetailRow

        @Prop({ default: false })
        private isInAllocation!: boolean

        @Prop({ default: "" })
        private treeTitle!: string

        private curTitle = getRegionName()

        tableConfig: TableConfig | null = null

        private displayDetailCollectDesc = false
        private descArr: any[] = []

        // private detailId = ""
        private rootTaskId = ""
        private rootTaskAccesskey = ""

        private columns: TableColumn[] = []

        private editIds: string[] = []
        private commonValue = 0

        private total = -1

        private detailLoading = true

        private wait_collect_data_num = "0"
        private wait_audit_collect_data_num = "0"
        private audited_collect_data_num = "0"

        private innerDetail: any = null

        protected pageSizes = [10, 20, 50, 100, 1000]

        private showTarget = false
        private showFillTarget = false

        private showExport = false

        private showV2Version = true

        private tableData: any[] = []

        private optList = [
            { label: "默认排序" },
            { label: "按任务进度正序" },
            { label: "按任务进度倒序" },
        ]

        private showTable = true
        private cacheTableData: any[] = []

        private optBtnText = this.optList[0].label

        private currentRow: any = null

        async created() {
            await this.initColumns()
        }

        private handleOptCommand(command: Record<string, any>) {
            this.optBtnText = command.label
            this.showTable = false
            this.$nextTick(() => (this.showTable = true))
        }

        private async initColumns() {
            this.showTarget = !!(await getSys4DCollectByKey(
                DCollectSysConfig.显示任务数量与目标数量
            ).catch(() => {}))

            this.showFillTarget = !!(await getSys4DCollectByKey(
                DCollectSysConfig.显示补充数据采集
            ).catch(() => {}))

            this.showExport = !!(await getSys4DCollectByKey(
                DCollectSysConfig.任务信息是否显示导出按钮
            ).catch(() => {}))

            if (this.showV2Version) {
                this.columns = columns2()
            } else {
                this.columns = columns(this.showTarget, this.showFillTarget)
            }
        }

        private updateTableColor() {
            if (this.showTarget) {
                applyTableStyles("task-table-list-detail", "showAdd")
            } else {
                applyTableStyles("task-table-list-detail", "hideAdd")
            }
            if (this.showFillTarget) {
                applyTableStyles("task-table-list-detail", "showFill")
            } else {
                applyTableStyles("task-table-list-detail", "hideFill")
            }
        }

        public noticeRegionIdUpdate(init: boolean) {
            this.init(init)
        }

        private level = "1"

        private init(init: boolean) {
            this.rootTaskId = this.detailRow.root_task_id || ""
            this.rootTaskAccesskey =
                this.detailRow.collect_root_task_access_key || ""

            const curTableParams = this.showV2Version
                ? "collect_task_detail_region_statistics2_new"
                : "collect_task_detail_region_statistics2"
            if (init) {
                this.tableConfig = tableConfig(
                    this.rootTaskAccesskey,
                    (d: any) => {
                        this.buildItemDesc(d)
                    },
                    curTableParams
                )
            }
            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig(
                    this.rootTaskAccesskey,
                    (d: any) => {
                        this.buildItemDesc(d)
                    },
                    curTableParams
                )
            })

            this.level = sessionStorage.getItem(CURRENT_REGION_NODE_LEVEL) || "1"
        }

        private regionTreeData: any = {
            level: 5,
            type: "scene",
            treeInfo: {
                manage_region_full_path: "",
            },
        }

        private regionTreeChange(r: any) {
            if (r.values && r.values.length > 0) {
                const codes = r.values[0].split(",")
                const region_names = r.displayValues[0].split("-")
                const region_code = codes[codes.length - 1]
                const region_name = region_names[region_names.length - 1]
                sessionStorage.setItem(CURRENT_TREE_REGION_NODE, region_code)
                sessionStorage.setItem(
                    CURRENT_REGION_NODE_LEVEL,
                    r.regionGrade + ""
                )
                this.currentRow = {
                    region_name,
                }
                this.init(false)
            } else {
                this.currentRow = null
            }
        }

        private changeVersion() {
            this.showV2Version = !this.showV2Version
            this.initColumns()
            this.init(false)
        }

        private buildItemDesc(d: any) {
            const c = cloneDeep(this.columns)

            const allArr: any[] = []

            function assignTips(
                item: Record<string, any>,
                dataDesc: Record<string, any>
            ) {
                let val = ""
                if (item.prop.includes("##")) {
                    const propArr = item.prop.split("##")
                    const arr: string[] = []
                    propArr.forEach((i: string) => {
                        if (dataDesc[i]) {
                            arr.push(dataDesc[i])
                        }
                    })
                    val = arr.join("\n")
                } else if (dataDesc[item.prop]) {
                    val = dataDesc[item.prop]
                }
                if (!val) {
                    return
                }
                Object.assign(item, {
                    showTips: true,
                    tips: val,
                })
                return val
            }

            c.forEach((i) => {
                const val = assignTips(i, d.data_desc)

                const innerA: any[] = []
                if (i.children) {
                    i.children.forEach((child) => {
                        const iVal = assignTips(child, d.data_desc)
                        if (iVal) {
                            innerA.push({
                                label: child.label,
                                desc: iVal,
                            })
                        }
                    })
                }
                if (val || i.children) {
                    allArr.push({
                        label: i.label,
                        desc: val,
                        children: innerA,
                    })
                }
            })

            this.descArr = allArr

            // console.log("allArr")
            // console.log(allArr)
            // this.$emit("getTips", allArr)

            this.columns = []

            this.$nextTick(() => {
                this.columns = cloneDeep(c)
            })
            // console.log("this.columns", this.columns)
        }

        private goAllocation(extraQuery = {}) {
            if (this.isInAllocation) {
                return this.$message.warning("正在分配中！")
            }
            this.$router.push({
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                    .allocation,
                query: {
                    id: this.$route.query.id as string,
                    rootTaskId:
                        this.rootTaskId || this.detailRow.root_task_id || "",
                    region: this.detailRow?.region_code_full || "",
                    regionLabel: this.getRegionData() || "",
                    from: routesMap.collectTaskManage.taskInfoManage.taskInfo
                        .detail,
                    ...extraQuery,
                },
            })
        }

        private getRegionData() {
            const detailRow: any = this.detailRow || {}
            return (
                (detailRow.province_name || "") +
                (detailRow.city_name || "") +
                (detailRow.district_name || "") +
                (detailRow.town_name || "") +
                (detailRow.village_name || "")
            )
        }

        private goReAllocation() {
            this.goAllocation({
                actionName: "re_auto_allocation",
            })
        }

        // private get unpreated() {
        //     return this.innerDetail?.unpreated || "0"
        // }

        // private get completion_rate() {
        //     return this.innerDetail?.completion_rate || "0"
        // }
        private exportExcel() {
            const regionName = this.treeTitle
            const { headerRows } = this.showV2Version
                ? this.getExcelHeaders2()
                : this.getExcelHeaders()

            // 处理数据行，对特定列进行换行处理
            const transformedData = this.tableData.map((row) => {
                return this.showV2Version
                    ? [
                          row.region_name, // 下级区域
                          row.task_num, // 已分配人数
                          row.task_wait_num, // 待采集人数
                          row.wait_audit_num, // 待审核数量
                          row.audit_success_num, // 审核通过数量
                          row.collect_num_in_task, // 任务内更新人数
                          row.doubtful_num, // 疑点数据总量
                          row.task_process, // 任务进度

                          row.task_collected_num, // 已采集人数
                          row.collect_num_in_task, // 任务内更新人数
                          row.collect_num_out_of_task, // 任务外更新人数
                          row.collect_add_num, // 新增人数
                          row.doubtful_num, // 疑点数据总量
                          row.not_contact_num, // 联系不上人数
                          row.not_local_num, // 非本区域人数
                          row.collect_by_others_num, // 被别人采集的人数
                          row.collect_process, // 采集进度
                          row.grid_user_num, // 采集员
                      ]
                    : [
                          row.region_name, // 下级区域
                          row.task_num, // 需采集人数
                          row.task_wait_num, // 待采集人数
                          row.task_collected_num, // 已采集人数
                          row.task_fill_num, // 补采数据
                          row.task_update_num, // 更新数据
                          row.wait_audit_num, // 待审核数量
                          row.audit_success_num, // 审核通过(次数)
                          row.collect_num, // 审核通过(人数)
                          row.not_contact_num, // 联系不上
                          row.not_local_num, // 非本区域居民
                          row.other_collect_num, // 其他区域采集
                          row.collect_process, // 总完成进度
                          `${row.collect_update_num}\n${row.collect_update_percentage}`, // 更新人数(更新进度)
                          `${row.collect_fill_num}\n${row.collect_fill_percentage}`, // 补采人数(补采进度)
                          `${row.collect_add_num}\n${row.collect_add_percentage}`, // 新增人数(新增进度)
                          row.grid_user_num, // 采集员
                      ]
            })

            this.exportToExcelCus(
                `${regionName}${this.sanitizeFileName(
                    this.detailRow?.title || ""
                )}下级区域列表导出数据`,
                headerRows,
                transformedData
            )
        }

        private sanitizeFileName(fileName: string): string {
            if (!fileName) {
                return "untitled"
            }

            return (
                fileName
                    // 替换Windows文件系统中的非法字符
                    .replace(/[<>:"/\\|?*]/g, "")
                    // 替换空格和点号
                    .replace(/\s+/g, "_")
                    .replace(/\.+/g, ".")
                    // 替换不可见字符和特殊字符
                    .replace(/[^\u0020-\u007E\u00A0-\uFFFF]/g, "")
                    // 移除开头的点号和空格
                    .replace(/^[.\s]+/, "")
                    // 移除结尾的点号和空格
                    .replace(/[.\s]+$/, "") ||
                // 如果文件名为空，返回默认名称
                "untitled"
            )
        }

        private getExcelHeaders() {
            const headerRow1 = [
                "下级区域",
                "任务总况",
                "",
                "",
                "分配情况",
                "",
                "审核情况",
                "",
                "",
                "疑点数据",
                "",
                "",
                "采集情况",
                "",
                "",
                "",
                "采集员",
            ]

            const headerRow2 = [
                "", // 下级区域
                "需采集人数",
                "待采集人数",
                "已采集人数", // 任务总况
                "补采数据",
                "更新数据", // 分配情况
                "待审核数量",
                "审核通过(次数)",
                "审核通过(人数)", // 审核情况
                "联系不上",
                "非本区域居民",
                "其他区域采集", // 疑点数据
                "总完成进度", // 采集情况
                "更新人数\n(更新进度)",
                "补采人数\n(补采进度)",
                "新增人数\n(新增进度)",
                "", // 采集员
            ]

            return {
                headerRows: [headerRow1, headerRow2],
            }
        }

        private getExcelHeaders2() {
            const headerRow1 = [
                "组织机构",
                "任务情况",
                "",
                "",
                "",
                "",
                "",
                "",
                "总采集人数",
                "",
                "",
                "",
                "疑点数据",
                "",
                "",
                "",
                "采集进度",
                "采集员",
            ]

            const headerRow2 = [
                "",
                "已分配人数",
                "待采集人数",
                "待审核数量",
                "审核通过数量",
                "任务内更新人数",
                "疑点数据总量",
                "任务进度",
                "已采集人数",
                "任务内更新人数",
                "任务外更新人数",
                "新增人数",
                "疑点数据总量",
                "联系不上人数",
                "非本区域人数",
                "被别人采集的人数",
                "采集进度",
                "",
            ]

            return {
                headerRows: [headerRow1, headerRow2],
            }
        }

        private exportToExcelCus(
            fileName: string,
            headerRows: string[][],
            rows: any[][]
        ): void {
            if (!fileName.endsWith(".xlsx")) {
                fileName += ".xlsx"
            }

            const wb = XLSX.utils.book_new()
            const data: any[][] = [...headerRows, ...rows]

            const ws = XLSX.utils.aoa_to_sheet(data)

            // 设置行高 (单位：磅)
            const rowHeights: any = {
                0: 30, // 第一行高度
                1: 40, // 第二行高度增加以适应换行
            }
            ws["!rows"] = Array(data.length)
                .fill(null)
                .map((_, idx) => ({
                    hpt: idx === 1 ? 40 : rowHeights[idx] || 35, // 增加所有行高以适应可能的换行
                }))

            // 设置列宽
            const colWidths = headerRows[1].map((_, index) => ({
                wch: Math.max(
                    20,
                    headerRows[0][index]?.length || 0,
                    (headerRows[1][index]?.split("\n")[0]?.length || 0) * 1.5, // 考虑换行后的文本长度
                    ...rows.map((row) => String(row[index] || "").length + 4)
                ),
            }))
            ws["!cols"] = colWidths

            // 设置合并单元格
            const merges: any[] = []
            let currentCol = 0
            headerRows[0].forEach((cell, index) => {
                if (cell) {
                    let span = 1
                    while (
                        index + span < headerRows[0].length &&
                        !headerRows[0][index + span]
                    ) {
                        span++
                    }
                    if (span > 1) {
                        merges.push({
                            s: { r: 0, c: currentCol },
                            e: { r: 0, c: currentCol + span - 1 },
                        })
                    }
                    if (!headerRows[1][index]) {
                        merges.push({
                            s: { r: 0, c: currentCol },
                            e: { r: 1, c: currentCol },
                        })
                    }
                    currentCol += span
                }
            })
            ws["!merges"] = merges

            // 样式设置
            for (let i = 0; i < headerRows[0].length; i++) {
                for (let r = 0; r < data.length; r++) {
                    const cellRef = XLSX.utils.encode_cell({ r, c: i })
                    if (!ws[cellRef]) ws[cellRef] = { v: "" }
                    if (!ws[cellRef].s) ws[cellRef].s = {}

                    // 基础样式
                    ws[cellRef].s = {
                        alignment: {
                            horizontal: "center",
                            vertical: "center",
                            wrapText: true, // 启用自动换行
                        },
                        border: {
                            top: { style: "thin", color: { rgb: "000000" } },
                            bottom: { style: "thin", color: { rgb: "000000" } },
                            left: { style: "thin", color: { rgb: "000000" } },
                            right: { style: "thin", color: { rgb: "000000" } },
                        },
                    }

                    // 表头特殊样式
                    if (r < 2) {
                        ws[cellRef].s.fill = {
                            fgColor: { rgb: "DDDDDD" },
                            patternType: "solid",
                        }
                        ws[cellRef].s.font = { bold: true, sz: 12 }
                    }

                    if (r === 2) {
                        ws[cellRef].s.fill = {
                            fgColor: { rgb: "FFF2F2" },
                            patternType: "solid",
                        } // 浅红色背景
                        ws[cellRef].s.font = {
                            color: { rgb: "FF0000" }, // 红色文字
                            sz: 12,
                        }
                    }

                    // 第一行和第二行之间的分隔线
                    if (r === 0) {
                        ws[cellRef].s.border.bottom = {
                            style: "medium",
                            color: { rgb: "000000" },
                        }
                    }
                }
            }

            XLSX.utils.book_append_sheet(wb, ws, "Sheet1")
            XLSX.writeFile(wb, fileName)
        }

        private setTarget() {
            this.$emit("setTarget")
        }

        private transferValue(value: any) {
            if (value === "-" || !value) {
                return 0
            }
            return parseFloat(value)
        }

        private handleData(data: any[]) {
            const d = cloneDeep(data)
            if (this.optBtnText === "按任务进度正序") {
                d.sort((a, b) => {
                    return (
                        this.transferValue(b.task_process) -
                        this.transferValue(a.task_process)
                    )
                })
            } else if (this.optBtnText === "按任务进度倒序") {
                d.sort((a, b) => {
                    return (
                        this.transferValue(a.task_process) -
                        this.transferValue(b.task_process)
                    )
                })
            }

            if (d && d.length && this.innerDetail) {
                d.unshift({
                    ...this.innerDetail,
                    isSum: true,
                    region_name:
                        this.currentRow?.region_name || this.curTitle || "总计",
                    // region_name: "（总计）" + this.innerDetail.region_name,
                })
            }

            this.tableData = d
            this.cacheTableData = d
            return d
        }

        private loaded(_data: any, r: any) {
            this.innerDetail = null
            this.$nextTick(() => {
                if (r) {
                    this.innerDetail = r.data_sum
                }
            })
        }

        private openEditNum(id: string, target_add_num: number) {
            this.editIds.push(id)
            this.editIds = this.editIds.filter((i) => i === id)
            const num = target_add_num || 0
            this.commonValue = num
        }

        private getViewDisplayInfo(key: string) {
            if (key === "collect_task") {
                return true
            }
            return DetailController.getPageViewsInDetail(key)
        }

        private getViewActionDisplayInfo(key: string) {
            return DetailController.getBtnViewsInDetail(key)
        }

        private onReset(id: string) {
            this.editIds = this.editIds.filter((i) => i !== id)
        }

        private setTotal(total: number) {
            this.total = total
            this.updateTableColor()
            return ""
        }

        private updateNum(id: string) {
            const config =
                DetailController.getPagePreFilterMetaInDetail("collect_task")

            sdk.core
                .model("collect_task")
                .action("update_target_add_num")
                .addInputs_parameter({
                    target_add_num: this.commonValue,
                })
                .updateInitialParams({
                    selected_list: [
                        {
                            id: (id + "") as any,
                            v: 0,
                        },
                    ],
                    prefilters: config.preFilter2Arr,
                })
                .execute()
                .then(() => {
                    this.$message.success("修改成功！")
                    this.refreshList()
                    this.onReset(id)

                    // this.getDetail()
                    // this.$emit("refresh")
                })
        }

        private handleRegionNameClick(row: any, index: number) {
            if (!index) return
            this.currentRow = row
            const regionLevelName = row.regionLevelName
            const idx = levelCodes.findIndex((i) => i === regionLevelName)
            sessionStorage.setItem(CURRENT_TREE_REGION_NODE, row.region_code)
            if (idx > -1) {
                sessionStorage.setItem(CURRENT_REGION_NODE_LEVEL, idx + 1 + "")
            } else {
                sessionStorage.setItem(CURRENT_REGION_NODE_LEVEL, "2")
            }
            this.init(false)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";

    .alert-box {
        padding-left: 20px;
        padding-right: 20px;
    }

    .table-box {
        min-width: 712px;
        flex: 1;
        display: flex;
        flex-direction: column;
        background-color: #fff;

        .right-top-box {
            display: flex;
            gap: 20px;

            .data-box {
                margin-bottom: 20px;
                height: 80px;
                padding: 15px 20px;
                flex: 1;
                background-color: #fff;
                cursor: pointer;

                .num {
                    font-size: 24px;
                    color: #5782ec;
                    line-height: 24px;
                    margin-left: 20px;
                }

                .small-num {
                    font-size: 14px;
                    color: #666666;
                    line-height: 30px;
                    margin-left: 20px;
                }
            }
        }

        .table-box {
            background-color: #fff;
            min-height: 510px;
            // padding: 20px;
            display: flex;
            flex-direction: column;
            flex: 1;

            // width: calc(100% + 40px);
            // margin-left: -20px;
            // padding-top: 32px;

            .table-tips {
                font-size: 14px;
                color: #e87005;
                line-height: 14px;
            }

            .region-info {
                font-size: 14px;
                color: #555555;
                line-height: 14px;
                margin-top: 20px;
            }

            .empty-box {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                flex: 1;

                img {
                    width: 92px;
                    height: 80px;
                }

                .empty-tips {
                    font-size: 14px;
                    color: #555555;
                    line-height: 14px;
                    margin-top: 30px;
                    margin-bottom: 20px;
                }
            }
        }

        /deep/ td.el-table__cell {
            padding-right: 1px;
        }
    }

    .input-box {
        height: 40px;
        overflow: hidden;
        display: flex;
        justify-content: center;

        /deep/ .el-input__inner {
            height: 40px;
            line-height: 40px;
        }
    }

    .opt-icon-box {
        margin-left: 5px;
        display: flex;
        flex-direction: column;
        font-size: 18px;
        justify-content: center;
        & > div {
            cursor: pointer;

            &:hover {
                color: #5782ec;
            }
        }
    }

    .opt-box {
        cursor: pointer;
        color: #5782ec;
    }

    .box-title {
        font-size: 16px;
        color: #000000;
        font-weight: 600;
        line-height: 16px;
    }

    .ml-auto {
        margin-left: auto;
    }

    .ml-15 {
        margin-left: 15px;
    }

    .headers-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 20px;
        padding-right: 20px;
        padding-top: 20px;
    }

    .item {
        width: 200px;
        height: 100px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #e1e1e1;
        flex: none;

        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 20px;

        gap: 10px;

        .title {
            font-size: 14px;
            font-weight: 600;
            color: #222222;
        }

        .value-label {
            color: #555555;
        }

        .value {
            color: #5782ec;
        }
    }

    .top-btn {
        margin-left: auto;
        align-self: flex-start;
        flex: none;
    }

    .dots {
        color: #409eff !important;
        font-weight: 700;
        font-size: 12px;

        &::after {
            content: ".";
            animation: dots 1.5s infinite;
        }
    }

    .process-box {
        width: 300px;
    }

    @keyframes dots {
        0% {
            content: ".";
        }
        33% {
            content: "..";
        }
        66% {
            content: "...";
        }
        100% {
            content: ".";
        }
    }

    .collect-task-table {
        /deep/ tbody {
            tr {
                &:first-child {
                    color: red;
                    font-weight: 600;
                    height: 50px;
                    font-size: 18px;

                    // &:hover {
                    //     & > td {
                    //         background-color: rgb(254, 240, 240);
                    //     }
                    // }

                    // td {
                    //     background-color: rgb(254, 240, 240);
                    // }
                }
            }
        }
    }

    .cur-city-5 {
        /deep/ tbody {
            tr {
                display: none;

                &:first-child {
                    display: block;
                }
            }
        }

        /deep/ .pagination {
            display: none;
        }
    }
    .active {
        cursor: pointer;
        color: #5782ec;
    }
    .area-select {
        width: 92px;
        color: #222222;
        font-size: 18px;
        flex: none;
    }
</style>
